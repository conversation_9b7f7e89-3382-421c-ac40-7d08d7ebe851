# Price Transform Functions
### AVGPRICE - Average Price
```python
real = AVGPRICE(open, high, low, close)
```

### MEDPRICE - Median Price
```python
real = MEDPRICE(high, low)
```

### TYPPRICE - Typical Price
```python
real = TYPPRICE(high, low, close)
```

### WCLPRICE - Weighted Close Price
```python
real = WCLPRICE(high, low, close)
```


[Documentation Index](../doc_index.md)

[FLOAT_RIGHTAll Function Groups](../funcs.md)