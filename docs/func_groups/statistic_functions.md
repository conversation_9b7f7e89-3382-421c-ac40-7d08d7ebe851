# Statistic Functions
### BETA - Beta
```python
real = BETA(real0, real1, timeperiod=5)
```

### CORREL - Pearson's Correlation Coefficient (r)
```python
real = CORREL(real0, real1, timeperiod=30)
```

### LINEARREG - Linear Regression
```python
real = LINEARREG(real, timeperiod=14)
```

### LINEARREG_ANGLE - Linear Regression Angle
```python
real = LINEARREG_ANGLE(real, timeperiod=14)
```

### LINEARREG_INTERCEPT - Linear Regression Intercept
```python
real = LINEARREG_INTERCEPT(real, timeperiod=14)
```

### LINEARREG_SLOPE - Linear Regression Slope
```python
real = LINEARREG_SLOPE(real, timeperiod=14)
```

### STDDEV - Standard Deviation
```python
real = STDDEV(real, timeperiod=5, nbdev=1)
```

### TSF - Time Series Forecast
```python
real = TSF(real, timeperiod=14)
```

### VAR - Variance
```python
real = VAR(real, timeperiod=5, nbdev=1)
```


[Documentation Index](../doc_index.md)

[FLOAT_RIGHTAll Function Groups](../funcs.md)
